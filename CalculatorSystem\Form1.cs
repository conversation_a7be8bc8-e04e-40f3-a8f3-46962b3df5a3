using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Threading;

namespace CalculatorSystem
{
    public partial class Form1 : Form
    {
        private CancellationTokenSource cancellationTokenSource;
        private List<string> originalData;
        private List<string> finalResults;
        private bool isProcessing = false;

        // 两阶段操作相关字段
        private Dictionary<int, List<string>> frequencyCombinations; // 频次 -> 组合列表
        private Dictionary<string, int> twoCombinationCounts; // 组合 -> 频次
        private bool isTwoStageMode = false;
        private HashSet<string> selectedCombinations; // 保存跨频次选择的组合

        public Form1()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            originalData = new List<string>();
            finalResults = new List<string>();
            frequencyCombinations = new Dictionary<int, List<string>>();
            twoCombinationCounts = new Dictionary<string, int>();
            selectedCombinations = new HashSet<string>();

            // 设置默认值
            numMaxNumber.Value = 33;
            numMagneticPoints.Value = 6;
            numTwoCombinationMin.Value = 5;
            numTwoCombinationMax.Value = 50;
            numResultMin.Value = 10;
            numResultMax.Value = 15;

            // 设置进度条
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Value = 0;

            // 初始化ListView
            InitializeListViews();

            UpdateUI();
        }

        /// <summary>
        /// 初始化ListView控件
        /// </summary>
        private void InitializeListViews()
        {
            // 初始化频次列表
            listViewFrequency.Columns.Add("频次", 80);
            listViewFrequency.Columns.Add("组合数", 100);
            listViewFrequency.SelectedIndexChanged += ListViewFrequency_SelectedIndexChanged;

            // 初始化组合列表
            listViewCombinations.Columns.Add("序号", 60);
            listViewCombinations.Columns.Add("组合", 120);
            listViewCombinations.Columns.Add("频次", 80);
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
            listViewCombinations.CheckBoxes = true;
        }

        /// <summary>
        /// 更新界面状态
        /// </summary>
        private void UpdateUI()
        {
            btnStart.Enabled = !isProcessing && !string.IsNullOrEmpty(txtFilePath.Text);
            btnCancel.Enabled = isProcessing;
            btnExport.Enabled = !isProcessing && finalResults.Count > 0;

            groupBoxSettings.Enabled = !isProcessing;
            groupBoxFile.Enabled = !isProcessing;

            // 两阶段模式界面控制
            if (isTwoStageMode)
            {
                groupBoxResults.Visible = false;
                groupBoxTwoStage.Visible = true;
                btnStart.Text = "分析2数组合";
            }
            else
            {
                groupBoxResults.Visible = true;
                groupBoxTwoStage.Visible = false;
                btnStart.Text = "开始计算";
            }

            // 更新导出按钮状态
            UpdateExportButtonState();

            // 两阶段模式下的按钮状态
            if (isTwoStageMode)
            {
                btnSelectAll.Enabled = !isProcessing && listViewCombinations.Items.Count > 0;
                btnClearSelection.Enabled = !isProcessing && selectedCombinations.Count > 0;
            }
        }

        /// <summary>
        /// 更新导出按钮状态
        /// </summary>
        private void UpdateExportButtonState()
        {
            if (isTwoStageMode)
            {
                int selectedCount = GetSelectedCombinationsCount();
                btnExportSelected.Enabled = selectedCount > 0 && !isProcessing;

                if (selectedCount > 0)
                {
                    // 统计选中组合的频次分布
                    var frequencyStats = selectedCombinations
                        .Where(c => twoCombinationCounts.ContainsKey(c))
                        .GroupBy(c => twoCombinationCounts[c])
                        .OrderBy(g => g.Key)
                        .Select(g => $"频次{g.Key}({g.Count()}个)")
                        .ToList();

                    string statsText = string.Join(", ", frequencyStats);
                    lblSelectedCount.Text = $"已选择: {selectedCount} 个组合 [{statsText}]";
                }
                else
                {
                    lblSelectedCount.Text = "已选择: 0 个组合";
                }
            }
        }

        /// <summary>
        /// 获取选中的组合数量
        /// </summary>
        private int GetSelectedCombinationsCount()
        {
            return selectedCombinations.Count;
        }

        /// <summary>
        /// 频次列表选择变化事件
        /// </summary>
        private void ListViewFrequency_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewFrequency.SelectedItems.Count > 0)
            {
                ShowCombinationsForSelectedFrequencies();
            }
        }

        /// <summary>
        /// 组合列表选择变化事件
        /// </summary>
        private void ListViewCombinations_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            // 更新全局选择状态
            var itemData = (dynamic)e.Item.Tag;
            string combination = itemData.Combination;

            if (e.Item.Checked)
            {
                selectedCombinations.Add(combination);
            }
            else
            {
                selectedCombinations.Remove(combination);
            }

            UpdateExportButtonState();
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = openFileDialog.FileName;
                LoadDataFile();
                UpdateUI();
            }
        }

        /// <summary>
        /// 加载数据文件
        /// </summary>
        private void LoadDataFile()
        {
            try
            {
                originalData.Clear();
                string[] lines = File.ReadAllLines(txtFilePath.Text, Encoding.UTF8);

                int validLines = 0;
                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();
                    if (!string.IsNullOrEmpty(line))
                    {
                        if (ValidateDataLine(line, i + 1))
                        {
                            originalData.Add(line);
                            validLines++;
                        }
                    }
                }

                lblStatus.Text = $"已加载 {validLines} 行有效数据";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "文件加载失败";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 验证数据行格式
        /// </summary>
        private bool ValidateDataLine(string line, int lineNumber)
        {
            try
            {
                string[] parts = line.Split('-');
                if (parts.Length < 2)
                {
                    MessageBox.Show($"第 {lineNumber} 行数据格式错误: 至少需要2个数字", "数据验证错误",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                List<int> numbers = new List<int>();
                foreach (string part in parts)
                {
                    if (!int.TryParse(part.Trim(), out int number))
                    {
                        MessageBox.Show($"第 {lineNumber} 行包含无效数字: {part}", "数据验证错误",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }

                    if (number < 1 || number > (int)numMaxNumber.Value)
                    {
                        MessageBox.Show($"第 {lineNumber} 行数字超出范围(1-{numMaxNumber.Value}): {number}",
                            "数据验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }

                    numbers.Add(number);
                }

                // 检查是否按升序排列且无重复
                for (int i = 1; i < numbers.Count; i++)
                {
                    if (numbers[i] <= numbers[i - 1])
                    {
                        MessageBox.Show($"第 {lineNumber} 行数据未按升序排列或存在重复: {line}",
                            "数据验证错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"第 {lineNumber} 行数据验证失败: {ex.Message}", "数据验证错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
        }

        /// <summary>
        /// 开始计算按钮点击事件
        /// </summary>
        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (originalData.Count == 0)
            {
                MessageBox.Show("请先选择并加载数据文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 验证参数
            if (numTwoCombinationMin.Value > numTwoCombinationMax.Value)
            {
                MessageBox.Show("2数组合频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (numResultMin.Value > numResultMax.Value)
            {
                MessageBox.Show("最终结果频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 询问用户选择操作模式
            var result = MessageBox.Show("请选择操作模式：\n\n是(Y) - 两阶段分析模式（先显示频次统计，再选择具体组合）\n否(N) - 传统计算模式（直接计算磁控点）",
                "选择操作模式", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            isTwoStageMode = (result == DialogResult.Yes);

            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                if (isTwoStageMode)
                {
                    await ProcessTwoStageAnalysisAsync(cancellationTokenSource.Token);
                }
                else
                {
                    await ProcessDataAsync(cancellationTokenSource.Token);
                }
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 取消计算按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
            }
        }

        /// <summary>
        /// 两阶段分析处理
        /// </summary>
        private async Task ProcessTwoStageAnalysisAsync(CancellationToken cancellationToken)
        {
            // 清空之前的选择状态
            selectedCombinations.Clear();

            await Task.Run(() =>
            {
                // 步骤1: 生成所有2数组合并统计频次
                UpdateStatus("正在生成2数组合...", 20);
                twoCombinationCounts = GenerateTwoCombinations(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 按频次分组
                UpdateStatus("正在分析频次分布...", 60);
                GroupCombinationsByFrequency();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 更新界面显示
                UpdateStatus("正在更新显示...", 90);
                UpdateFrequencyDisplay();

                UpdateStatus($"分析完成，共发现 {twoCombinationCounts.Count} 个不同的2数组合", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 按频次分组组合
        /// </summary>
        private void GroupCombinationsByFrequency()
        {
            frequencyCombinations.Clear();

            foreach (var kvp in twoCombinationCounts)
            {
                int frequency = kvp.Value;
                string combination = kvp.Key;

                if (!frequencyCombinations.ContainsKey(frequency))
                {
                    frequencyCombinations[frequency] = new List<string>();
                }

                frequencyCombinations[frequency].Add(combination);
            }
        }

        /// <summary>
        /// 更新频次显示
        /// </summary>
        private void UpdateFrequencyDisplay()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(UpdateFrequencyDisplay));
                return;
            }

            listViewFrequency.Items.Clear();

            // 按频次排序显示
            var sortedFrequencies = frequencyCombinations.Keys.OrderBy(f => f).ToList();

            foreach (int frequency in sortedFrequencies)
            {
                int count = frequencyCombinations[frequency].Count;
                var item = new ListViewItem(frequency.ToString());
                item.SubItems.Add(count.ToString());
                item.Tag = frequency;
                listViewFrequency.Items.Add(item);
            }

            lblStatus.Text = $"频次分析完成，共 {sortedFrequencies.Count} 个不同频次";
            lblStatus.ForeColor = Color.Green;
        }

        /// <summary>
        /// 显示选中频次的组合
        /// </summary>
        private void ShowCombinationsForSelectedFrequencies()
        {
            // 临时禁用事件处理，避免在重建列表时触发选择变化事件
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            listViewCombinations.Items.Clear();

            var selectedFrequencies = new List<int>();
            foreach (ListViewItem item in listViewFrequency.SelectedItems)
            {
                selectedFrequencies.Add((int)item.Tag);
            }

            if (selectedFrequencies.Count == 0)
            {
                listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
                return;
            }

            int index = 1;
            foreach (int frequency in selectedFrequencies.OrderBy(f => f))
            {
                if (frequencyCombinations.ContainsKey(frequency))
                {
                    var combinations = frequencyCombinations[frequency].OrderBy(c => c).ToList();

                    foreach (string combination in combinations)
                    {
                        var item = new ListViewItem(index.ToString());
                        item.SubItems.Add(combination);
                        item.SubItems.Add(frequency.ToString());
                        item.Tag = new { Combination = combination, Frequency = frequency };

                        // 恢复之前的选择状态
                        item.Checked = selectedCombinations.Contains(combination);

                        listViewCombinations.Items.Add(item);
                        index++;
                    }
                }
            }

            lblCombinationList.Text = $"具体组合（共 {listViewCombinations.Items.Count} 个）：";

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
        }

        /// <summary>
        /// 异步处理数据
        /// </summary>
        private async Task ProcessDataAsync(CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                // 步骤1: 生成所有2数组合并统计频次
                UpdateStatus("正在生成2数组合...", 10);
                var twoCombinations = GenerateTwoCombinations(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 筛选有效的2数组合
                UpdateStatus("正在筛选有效2数组合...", 20);
                var validTwoCombinations = FilterTwoCombinations(twoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 生成磁控点组合
                UpdateStatus("正在生成磁控点组合...", 30);
                var magneticPoints = GenerateMagneticPoints(validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤4: 统计磁控点频次
                UpdateStatus("正在统计磁控点频次...", 70);
                var magneticPointCounts = CountMagneticPoints(magneticPoints, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤5: 筛选最终结果
                UpdateStatus("正在筛选最终结果...", 85);
                var filteredResults = FilterFinalResults(magneticPointCounts, validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤6: 更新界面
                UpdateStatus("正在更新结果显示...", 95);
                UpdateResults(filteredResults);

                UpdateStatus($"计算完成，共生成 {filteredResults.Count} 个结果", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, int>(UpdateStatus), message, progress);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Blue;
            progressBar.Value = Math.Min(progress, 100);
        }

        /// <summary>
        /// 生成所有2数组合并统计频次
        /// </summary>
        private Dictionary<string, int> GenerateTwoCombinations(CancellationToken cancellationToken)
        {
            var combinations = new Dictionary<string, int>();

            foreach (string line in originalData)
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] parts = line.Split('-');
                List<int> numbers = parts.Select(int.Parse).ToList();

                // 生成所有2数组合
                for (int i = 0; i < numbers.Count - 1; i++)
                {
                    for (int j = i + 1; j < numbers.Count; j++)
                    {
                        string combination = $"{numbers[i]}-{numbers[j]}";
                        if (combinations.ContainsKey(combination))
                        {
                            combinations[combination]++;
                        }
                        else
                        {
                            combinations[combination] = 1;
                        }
                    }
                }
            }

            return combinations;
        }

        /// <summary>
        /// 筛选有效的2数组合
        /// </summary>
        private List<string> FilterTwoCombinations(Dictionary<string, int> combinations, CancellationToken cancellationToken)
        {
            var validCombinations = new List<string>();
            int minCount = (int)numTwoCombinationMin.Value;
            int maxCount = (int)numTwoCombinationMax.Value;

            foreach (var kvp in combinations)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    validCombinations.Add(kvp.Key);
                }
            }

            return validCombinations;
        }

        /// <summary>
        /// 生成磁控点组合
        /// </summary>
        private List<string> GenerateMagneticPoints(List<string> validTwoCombinations, CancellationToken cancellationToken)
        {
            var magneticPoints = new List<string>();
            int magneticPointCount = (int)numMagneticPoints.Value;
            int maxNumber = (int)numMaxNumber.Value;

            // 创建所有可能的数字集合
            var allNumbers = Enumerable.Range(1, maxNumber).ToList();

            foreach (string twoCombination in validTwoCombinations)
            {
                cancellationToken.ThrowIfCancellationRequested();

                string[] parts = twoCombination.Split('-');
                int num1 = int.Parse(parts[0]);
                int num2 = int.Parse(parts[1]);

                // 从剩余数字中选择需要的数量
                var remainingNumbers = allNumbers.Where(n => n != num1 && n != num2).ToList();
                int needCount = magneticPointCount - 2;

                // 生成所有可能的组合
                var combinations = GetCombinations(remainingNumbers, needCount);

                foreach (var combination in combinations)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var fullCombination = new List<int> { num1, num2 };
                    fullCombination.AddRange(combination);
                    fullCombination.Sort();

                    string magneticPoint = string.Join("-", fullCombination);
                    magneticPoints.Add(magneticPoint);
                }
            }

            return magneticPoints;
        }

        /// <summary>
        /// 获取组合（C(n,r)）- 优化版本
        /// </summary>
        private IEnumerable<List<int>> GetCombinations(List<int> list, int length)
        {
            if (length == 0)
                yield return new List<int>();
            else if (length == 1)
            {
                foreach (var item in list)
                    yield return new List<int> { item };
            }
            else
            {
                for (int i = 0; i <= list.Count - length; i++)
                {
                    var head = list[i];
                    var tail = list.Skip(i + 1).ToList();

                    foreach (var combination in GetCombinations(tail, length - 1))
                    {
                        var result = new List<int> { head };
                        result.AddRange(combination);
                        yield return result;
                    }
                }
            }
        }

        /// <summary>
        /// 统计磁控点频次
        /// </summary>
        private Dictionary<string, int> CountMagneticPoints(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            var counts = new Dictionary<string, int>();

            foreach (string point in magneticPoints)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (counts.ContainsKey(point))
                {
                    counts[point]++;
                }
                else
                {
                    counts[point] = 1;
                }
            }

            return counts;
        }

        /// <summary>
        /// 筛选最终结果并剔除无效数据
        /// </summary>
        private List<string> FilterFinalResults(Dictionary<string, int> magneticPointCounts,
            List<string> validTwoCombinations, CancellationToken cancellationToken)
        {
            var results = new List<string>();
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;
            int maxNumber = (int)numMaxNumber.Value;

            // 获取所有可能的2数组合
            var allPossibleTwoCombinations = new HashSet<string>();
            for (int i = 1; i <= maxNumber - 1; i++)
            {
                for (int j = i + 1; j <= maxNumber; j++)
                {
                    allPossibleTwoCombinations.Add($"{i}-{j}");
                }
            }

            // 获取无效的2数组合（频次不在范围内的，包括未出现的和出现但不在范围内的）
            var invalidTwoCombinations = allPossibleTwoCombinations.Except(validTwoCombinations).ToHashSet();

            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查频次是否在范围内
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    // 检查是否包含无效的2数组合
                    if (!ContainsInvalidTwoCombination(kvp.Key, invalidTwoCombinations))
                    {
                        results.Add(kvp.Key);
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// 检查磁控点是否包含无效的2数组合
        /// </summary>
        private bool ContainsInvalidTwoCombination(string magneticPoint, HashSet<string> invalidTwoCombinations)
        {
            string[] parts = magneticPoint.Split('-');
            List<int> numbers = parts.Select(int.Parse).ToList();

            // 检查所有2数组合
            for (int i = 0; i < numbers.Count - 1; i++)
            {
                for (int j = i + 1; j < numbers.Count; j++)
                {
                    string combination = $"{numbers[i]}-{numbers[j]}";
                    if (invalidTwoCombinations.Contains(combination))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 更新结果显示
        /// </summary>
        private void UpdateResults(List<string> results)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<List<string>>(UpdateResults), results);
                return;
            }

            finalResults = results;
            lblResultCount.Text = $"结果数量: {results.Count}";

            // 显示前100个结果
            var displayResults = results.Take(100).ToList();
            txtResults.Text = string.Join(Environment.NewLine, displayResults);

            if (results.Count > 100)
            {
                txtResults.Text += Environment.NewLine + $"... 还有 {results.Count - 100} 个结果未显示";
            }

            lblStatus.Text = $"计算完成，共生成 {results.Count} 个结果";
            lblStatus.ForeColor = Color.Green;
        }

        /// <summary>
        /// 导出结果按钮点击事件
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            if (finalResults.Count == 0)
            {
                MessageBox.Show("没有可导出的结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            saveFileDialog.FileName = $"磁控点计算结果_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 创建导出内容
                    var exportContent = new StringBuilder();
                    /*
                    exportContent.AppendLine("# 离子镀膜磁控点分布运算结果");
                    exportContent.AppendLine($"# 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    exportContent.AppendLine($"# 参数设置:");
                    exportContent.AppendLine($"#   最大数值: {numMaxNumber.Value}");
                    exportContent.AppendLine($"#   磁控点位数: {numMagneticPoints.Value}");
                    exportContent.AppendLine($"#   2数组合频次范围: {numTwoCombinationMin.Value}-{numTwoCombinationMax.Value}");
                    exportContent.AppendLine($"#   最终结果频次范围: {numResultMin.Value}-{numResultMax.Value}");
                    exportContent.AppendLine($"# 结果数量: {finalResults.Count}");
                    exportContent.AppendLine("#");
                    */
                    foreach (string result in finalResults)
                    {
                        exportContent.AppendLine(result);
                    }

                    File.WriteAllText(saveFileDialog.FileName, exportContent.ToString(), Encoding.UTF8);

                    MessageBox.Show($"结果已成功导出到:\n{saveFileDialog.FileName}", "导出成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = $"结果已导出到: {Path.GetFileName(saveFileDialog.FileName)}";
                    lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "导出失败";
                    lblStatus.ForeColor = Color.Red;
                }
            }
        }

        /// <summary>
        /// 导出选中组合按钮点击事件
        /// </summary>
        private async void btnExportSelected_Click(object sender, EventArgs e)
        {
            if (selectedCombinations.Count == 0)
            {
                MessageBox.Show("请先选择要导出的组合", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 确认是否继续计算磁控点
            var result = MessageBox.Show($"将基于您选中的 {selectedCombinations.Count} 个2数组合计算磁控点分布。\n\n这可能需要一些时间，是否继续？",
                "确认计算", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes) return;

            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                await ProcessSelectedCombinationsAsync(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 处理选中组合的磁控点计算
        /// </summary>
        private async Task ProcessSelectedCombinationsAsync(CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                // 步骤1: 将选中的组合转换为有效组合列表
                UpdateStatus("正在准备选中的2数组合...", 10);
                var validTwoCombinations = selectedCombinations.ToList();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 生成磁控点组合
                UpdateStatus("正在生成磁控点组合...", 30);
                var magneticPoints = GenerateMagneticPoints(validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 统计磁控点频次
                UpdateStatus("正在统计磁控点频次...", 70);
                var magneticPointCounts = CountMagneticPoints(magneticPoints, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤4: 筛选最终结果（基于选中的组合，不使用频次范围）
                UpdateStatus("正在筛选最终结果...", 85);
                var filteredResults = FilterResultsBySelectedCombinations(magneticPointCounts, validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤5: 导出结果
                UpdateStatus("正在导出结果...", 95);
                ExportMagneticPointResults(filteredResults);

                UpdateStatus($"计算完成，共生成 {filteredResults.Count} 个磁控点结果", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 基于选中组合筛选磁控点结果
        /// </summary>
        private List<string> FilterResultsBySelectedCombinations(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            var results = new List<string>();
            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);

            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的2数组合
                if (ContainsSelectedTwoCombination(kvp.Key, selectedCombinationsSet))
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 检查磁控点是否包含选中的2数组合
        /// </summary>
        private bool ContainsSelectedTwoCombination(string magneticPoint, HashSet<string> selectedCombinations)
        {
            string[] parts = magneticPoint.Split('-');
            List<int> numbers = parts.Select(int.Parse).ToList();

            // 检查所有2数组合
            for (int i = 0; i < numbers.Count - 1; i++)
            {
                for (int j = i + 1; j < numbers.Count; j++)
                {
                    string combination = $"{numbers[i]}-{numbers[j]}";
                    if (selectedCombinations.Contains(combination))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 导出磁控点结果
        /// </summary>
        private void ExportMagneticPointResults(List<string> results)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<List<string>>(ExportMagneticPointResults), results);
                return;
            }

            if (results.Count == 0)
            {
                MessageBox.Show("基于选中的2数组合未生成任何磁控点结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            saveFileDialog.FileName = $"基于选中组合的磁控点结果_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var exportContent = new StringBuilder();
                    exportContent.AppendLine("# 基于选中2数组合的磁控点分布运算结果");
                    exportContent.AppendLine($"# 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    exportContent.AppendLine($"# 参数设置:");
                    exportContent.AppendLine($"#   最大数值: {numMaxNumber.Value}");
                    exportContent.AppendLine($"#   磁控点位数: {numMagneticPoints.Value}");
                    exportContent.AppendLine($"#   基于选中的2数组合数量: {selectedCombinations.Count}");
                    exportContent.AppendLine($"# 磁控点结果数量: {results.Count}");
                    exportContent.AppendLine("#");
                    exportContent.AppendLine("# 选中的2数组合:");
                    foreach (string combination in selectedCombinations.OrderBy(c => c))
                    {
                        exportContent.AppendLine($"#   {combination}");
                    }
                    exportContent.AppendLine("#");
                    exportContent.AppendLine("# 磁控点结果:");
                    exportContent.AppendLine("#");

                    foreach (string result in results.OrderBy(r => r))
                    {
                        exportContent.AppendLine(result);
                    }

                    File.WriteAllText(saveFileDialog.FileName, exportContent.ToString(), Encoding.UTF8);

                    MessageBox.Show($"磁控点结果已成功导出到:\n{saveFileDialog.FileName}\n\n共导出 {results.Count} 个磁控点组合",
                        "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = $"已导出 {results.Count} 个磁控点结果到: {Path.GetFileName(saveFileDialog.FileName)}";
                    lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "导出失败";
                    lblStatus.ForeColor = Color.Red;
                }
            }
        }

        /// <summary>
        /// 获取选中的组合
        /// </summary>
        private List<dynamic> GetSelectedCombinations()
        {
            var result = new List<dynamic>();

            // 从全局选择集合中获取组合信息
            foreach (string combination in selectedCombinations)
            {
                if (twoCombinationCounts.ContainsKey(combination))
                {
                    result.Add(new { Combination = combination, Frequency = twoCombinationCounts[combination] });
                }
            }

            return result;
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = true;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Add(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }

        /// <summary>
        /// 清空选择按钮点击事件
        /// </summary>
        private void btnClearSelection_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = false;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Remove(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }
    }
}
